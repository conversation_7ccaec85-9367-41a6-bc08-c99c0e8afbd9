import {
  DevicePhoneMobileIcon,
  CalendarDaysIcon,
  CreditCardIcon,
  TruckIcon,
} from '@heroicons/react/24/outline';

const steps = [
  {
    id: '01',
    name: 'Téléchargez l\'app',
    description: 'Créez votre compte en quelques minutes avec votre email ou via Google/Facebook/Apple.',
    icon: DevicePhoneMobileIcon,
  },
  {
    id: '02',
    name: 'Réservez votre créneau',
    description: 'Choisissez votre service, date, heure et localisez votre véhicule via GPS.',
    icon: CalendarDaysIcon,
  },
  {
    id: '03',
    name: 'Payez en sécurité',
    description: 'R<PERSON>glez en ligne ou réservez en espèces avec empreinte bancaire de garantie.',
    icon: CreditCardIcon,
  },
  {
    id: '04',
    name: 'Profitez du service',
    description: 'Notre technicien se rend à votre véhicule et effectue un lavage éco-responsable.',
    icon: TruckIcon,
  },
];

export function HowItWorks() {
  return (
    <div className="bg-gray-50 py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-blue-600">
            Simple et efficace
          </h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Comment ça marche ?
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            En seulement 4 étapes simples, votre véhicule sera lavé par nos experts 
            avec notre technologie éco-responsable.
          </p>
        </div>
        
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-4">
            {steps.map((step) => (
              <div key={step.id} className="flex flex-col items-center text-center">
                <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600 text-white">
                  <step.icon className="h-8 w-8" aria-hidden="true" />
                </div>
                <div className="mb-2 flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-sm font-semibold text-blue-600">
                  {step.id}
                </div>
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  {step.name}
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  {step.description}
                </dd>
              </div>
            ))}
          </dl>
        </div>
        
        <div className="mt-16 flex justify-center">
          <a
            href="/booking"
            className="rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
          >
            Commencer maintenant
          </a>
        </div>
      </div>
    </div>
  );
}
