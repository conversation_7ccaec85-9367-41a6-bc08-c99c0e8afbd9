import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinC<PERSON>umn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from './user.entity';

export enum NotificationType {
  RESERVATION_CONFIRMED = 'RESERVATION_CONFIRMED',
  REMINDER = 'REMINDER',
  TECHNICIAN_STARTED = 'TECHNICIAN_STARTED',
  TECHNICIAN_FINISHED = 'TECHNICIAN_FINISHED',
  PAYMENT_RECEIVED = 'PAYMENT_RECEIVED',
  CREDITS_LOW = 'CREDITS_LOW',
}

@Entity('notifications')
export class Notification {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty()
  @Column('uuid')
  userId: string;

  @ApiProperty({ enum: NotificationType })
  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @ApiProperty()
  @Column()
  title: string;

  @ApiProperty()
  @Column('text')
  message: string;

  @ApiProperty()
  @Column({ default: false })
  isRead: boolean;

  @ApiProperty()
  @Column('jsonb', { nullable: true })
  data: any;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.notifications)
  @JoinColumn({ name: 'userId' })
  user: User;
}
