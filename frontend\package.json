{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@stripe/react-stripe-js": "^3.9.2", "@stripe/stripe-js": "^7.9.0", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "axios": "^1.11.0", "date-fns": "^4.1.0", "firebase": "^12.1.0", "framer-motion": "^12.23.12", "next": "15.5.2", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-intersection-observer": "^9.16.0", "stripe": "^18.4.0", "zod": "^4.1.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}