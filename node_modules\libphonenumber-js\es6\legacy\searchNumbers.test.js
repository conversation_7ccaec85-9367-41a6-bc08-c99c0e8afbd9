function _createForOfIteratorHelperLoose(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (t) return (t = t.call(r)).next.bind(t); if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var o = 0; return function () { return o >= r.length ? { done: !0 } : { done: !1, value: r[o++] }; }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
import searchNumbers from './searchNumbers.js';
import metadata from '../../metadata.min.json' with { type: 'json' };
describe('searchNumbers', function () {
  it('should iterate', function () {
    var expectedNumbers = [{
      country: 'RU',
      phone: '8005553535',
      // number   : '+7 (800) 555-35-35',
      startsAt: 14,
      endsAt: 32
    }, {
      country: 'US',
      phone: '2133734253',
      // number   : '(*************',
      startsAt: 41,
      endsAt: 55
    }];
    for (var _iterator = _createForOfIteratorHelperLoose(searchNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)), _step; !(_step = _iterator()).done;) {
      var number = _step.value;
      expect(number).to.deep.equal(expectedNumbers.shift());
    }
    expect(expectedNumbers.length).to.equal(0);
  });
});
//# sourceMappingURL=searchNumbers.test.js.map