import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { getDatabaseConfig } from './config/database.config';

// Entities
import { User } from './entities/user.entity';
import { Service } from './entities/service.entity';
import { Reservation } from './entities/reservation.entity';
import { Payment } from './entities/payment.entity';
import { Photo } from './entities/photo.entity';
import { Review } from './entities/review.entity';
import { Notification } from './entities/notification.entity';
import { LoyaltyTransaction } from './entities/loyalty-transaction.entity';
import { Referral } from './entities/referral.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: getDatabaseConfig,
      inject: [ConfigService],
    }),

    // Rate limiting
    ThrottlerModule.forRoot([{
      ttl: 60000, // 1 minute
      limit: 100, // 100 requests per minute
    }]),

    // Feature modules will be added here
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
