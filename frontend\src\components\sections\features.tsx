import {
  ClockIcon,
  MapPinIcon,
  CreditCardIcon,
  StarIcon,
  CameraIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'Service 24h/24, 7j/7',
    description: 'Réservez votre lavage à tout moment, même en dehors des heures d\'ouverture traditionnelles.',
    icon: ClockIcon,
  },
  {
    name: 'Géolocalisation précise',
    description: 'Notre système GPS localise votre véhicule avec précision pour un service optimal.',
    icon: MapPinIcon,
  },
  {
    name: 'Paiement sécurisé',
    description: 'Payez en ligne ou en espèces avec garantie bancaire. Système de crédits prépayés disponible.',
    icon: CreditCardIcon,
  },
  {
    name: 'Programme de fidélité',
    description: 'Gagnez des points à chaque lavage et bénéficiez de réductions exclusives.',
    icon: StarIcon,
  },
  {
    name: 'Photos avant/après',
    description: 'Recevez des photos de votre véhicule avant et après le lavage pour votre satisfaction.',
    icon: CameraIcon,
  },
  {
    name: 'Technologie éco-responsable',
    description: 'Buse supersonique économe en eau et produits biodégradables respectueux de l\'environnement.',
    icon: ShieldCheckIcon,
  },
];

export function Features() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-blue-600">
            Lavage moderne
          </h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Tout ce dont vous avez besoin pour un lavage parfait
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Notre service combine technologie avancée et respect de l'environnement 
            pour vous offrir la meilleure expérience de lavage automobile.
          </p>
        </div>
        
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
            {features.map((feature) => (
              <div key={feature.name} className="relative pl-16">
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  <div className="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                    <feature.icon className="h-6 w-6 text-white" aria-hidden="true" />
                  </div>
                  {feature.name}
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  {feature.description}
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </div>
  );
}
