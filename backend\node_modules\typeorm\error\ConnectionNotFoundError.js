"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionNotFoundError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when consumer tries to get connection that does not exist.
 */
class ConnectionNotFoundError extends TypeORMError_1.TypeORMError {
    constructor(name) {
        super(`Connection "${name}" was not found.`);
    }
}
exports.ConnectionNotFoundError = ConnectionNotFoundError;

//# sourceMappingURL=ConnectionNotFoundError.js.map
