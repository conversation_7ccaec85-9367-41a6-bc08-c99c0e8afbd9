import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import { Toaster } from "react-hot-toast";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Wash 2 Go - Lavage Automobile Éco-Responsable",
  description: "Réservez votre lavage automobile extérieur éco-responsable avec buse supersonique. Service 24h/24, 7j/7 avec géolocalisation et paiement sécurisé.",
  keywords: "lavage automobile, éco-responsable, réservation, géolocalisation, paiement sécurisé",
  authors: [{ name: "Wash 2 Go" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "Wash 2 Go - Lavage Automobile Éco-Responsable",
    description: "Réservez votre lavage automobile extérieur éco-responsable avec buse supersonique.",
    type: "website",
    locale: "fr_FR",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50 antialiased`}>
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
