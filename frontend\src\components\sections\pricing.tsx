import { CheckIcon } from '@heroicons/react/20/solid';

const tiers = [
  {
    name: 'Lavage Standard',
    id: 'standard',
    href: '/booking?service=standard',
    price: '15',
    description: 'Parfait pour un entretien régulier de votre véhicule.',
    features: [
      'Lavage extérieur complet',
      'Buse supersonique éco-responsable',
      'Séchage professionnel',
      'Photos avant/après',
      'Points de fidélité',
    ],
    duration: '30 min',
    mostPopular: false,
  },
  {
    name: 'Lavage Premium',
    id: 'premium',
    href: '/booking?service=premium',
    price: '25',
    description: 'Le choix idéal pour un véhicule impeccable.',
    features: [
      'Tout du lavage Standard',
      'Nettoyage des jantes',
      'Protection carrosserie',
      'Nettoyage des vitres',
      'Aspirateur tapis',
      'Double points de fidélité',
    ],
    duration: '45 min',
    mostPopular: true,
  },
  {
    name: '<PERSON><PERSON> Complet',
    id: 'complete',
    href: '/booking?service=complete',
    price: '35',
    description: 'Le service le plus complet pour votre véhicule.',
    features: [
      'Tout du lavage Premium',
      'Nettoyage intérieur complet',
      'Cire de protection',
      'Nettoyage des sièges',
      'Désodorisant',
      'Triple points de fidélité',
      'Garantie satisfaction',
    ],
    duration: '60 min',
    mostPopular: false,
  },
];

export function Pricing() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-base font-semibold leading-7 text-blue-600">Tarifs</h2>
          <p className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Choisissez le service qui vous convient
          </p>
        </div>
        <p className="mx-auto mt-6 max-w-2xl text-center text-lg leading-8 text-gray-600">
          Tous nos services incluent notre technologie éco-responsable et notre programme de fidélité.
          Paiement sécurisé en ligne ou en espèces.
        </p>
        
        <div className="isolate mx-auto mt-16 grid max-w-md grid-cols-1 gap-y-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-x-8 lg:gap-y-0">
          {tiers.map((tier, tierIdx) => (
            <div
              key={tier.id}
              className={`flex flex-col justify-between rounded-3xl bg-white p-8 ring-1 ring-gray-200 xl:p-10 ${
                tier.mostPopular ? 'lg:z-10 lg:rounded-b-none' : 'lg:mt-8'
              }`}
            >
              <div>
                <div className="flex items-center justify-between gap-x-4">
                  <h3
                    id={tier.id}
                    className={`text-lg font-semibold leading-8 ${
                      tier.mostPopular ? 'text-blue-600' : 'text-gray-900'
                    }`}
                  >
                    {tier.name}
                  </h3>
                  {tier.mostPopular ? (
                    <p className="rounded-full bg-blue-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-blue-600">
                      Plus populaire
                    </p>
                  ) : null}
                </div>
                <p className="mt-4 text-sm leading-6 text-gray-600">{tier.description}</p>
                <p className="mt-6 flex items-baseline gap-x-1">
                  <span className="text-4xl font-bold tracking-tight text-gray-900">{tier.price}€</span>
                  <span className="text-sm font-semibold leading-6 text-gray-600">/ {tier.duration}</span>
                </p>
                <ul role="list" className="mt-8 space-y-3 text-sm leading-6 text-gray-600">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex gap-x-3">
                      <CheckIcon className="h-6 w-5 flex-none text-blue-600" aria-hidden="true" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <a
                href={tier.href}
                aria-describedby={tier.id}
                className={`mt-8 block rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 ${
                  tier.mostPopular
                    ? 'bg-blue-600 text-white shadow-sm hover:bg-blue-500 focus-visible:outline-blue-600'
                    : 'text-blue-600 ring-1 ring-inset ring-blue-200 hover:ring-blue-300'
                }`}
              >
                Réserver ce service
              </a>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
