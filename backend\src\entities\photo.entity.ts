import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Reservation } from './reservation.entity';
import { User } from './user.entity';

@Entity('photos')
export class Photo {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty()
  @Column('uuid')
  reservationId: string;

  @ApiProperty()
  @Column()
  filePath: string;

  @ApiProperty()
  @Column()
  fileName: string;

  @ApiProperty()
  @Column({ nullable: true })
  fileSize: number;

  @ApiProperty()
  @Column()
  isBefore: boolean;

  @ApiProperty()
  @Column('uuid', { nullable: true })
  uploadedBy: string;

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;

  // Relations
  @ManyToOne(() => Reservation, (reservation) => reservation.photos)
  @JoinColumn({ name: 'reservationId' })
  reservation: Reservation;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'uploadedBy' })
  uploader: User;
}
